from flask import jsonify
import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>

def run(task, speaker):
    """
    Enhanced email sender with proper SMTP support
    """
    try:
        # Extract email parameters from task
        input_text = task.get("input", "")
        recipient = task.get("to", "")
        subject = task.get("subject", f"Message from Agent Lee - {speaker}")
        body = task.get("body", input_text)

        if not recipient:
            # Try to extract email from input text
            import re
            email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            emails = re.findall(email_pattern, input_text)
            if emails:
                recipient = emails[0]
            else:
                return jsonify({"error": "No recipient email address provided."})

        if not body:
            return jsonify({"error": "No email content provided."})

        # Email configuration from environment
        smtp_server = os.getenv("SMTP_SERVER", "smtp.gmail.com")
        smtp_port = int(os.getenv("SMTP_PORT", "587"))
        sender_email = os.getenv("SENDER_EMAIL", "<EMAIL>")
        sender_password = os.getenv("SENDER_PASSWORD", "")

        if not sender_password:
            # Fallback to EmailJS simulation
            return jsonify({
                "status": "Email queued (simulated)",
                "to": recipient,
                "subject": subject,
                "method": "emailjs_fallback",
                "speaker": speaker,
                "tool": "email_sender"
            })

        # Create message
        message = MIMEMultipart()
        message["From"] = sender_email
        message["To"] = recipient
        message["Subject"] = subject

        # Add body
        message.attach(MIMEText(body, "plain"))

        # Send email
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls()
            server.login(sender_email, sender_password)
            server.send_message(message)

        return jsonify({
            "status": "Email sent successfully",
            "to": recipient,
            "subject": subject,
            "method": "smtp",
            "speaker": speaker,
            "tool": "email_sender"
        })

    except Exception as e:
        return jsonify({
            "error": f"Failed to send email: {str(e)}",
            "speaker": speaker,
            "tool": "email_sender"
        })

# Legacy compatibility
def main():
    import sys
    text = sys.argv[1] if len(sys.argv) > 1 else ""
    result = run({"input": text}, "cli_user")
    print(result.get_json().get("status", "Email processing failed"))

if __name__ == "__main__":
    main()
