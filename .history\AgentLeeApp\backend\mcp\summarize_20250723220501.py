from flask import jsonify

def run(task, speaker):
    """
    Enhanced text summarization tool with multi-speaker support
    """
    text = task.get("input", "")

    if not text:
        return jsonify({"error": "No input text to summarize."})

    # Enhanced summarization logic
    if len(text) <= 100:
        summary = text
        summary_type = "full_text"
    else:
        # Extract key sentences (simple implementation)
        sentences = text.split('. ')
        if len(sentences) > 3:
            summary = '. '.join(sentences[:3]) + "..."
            summary_type = "truncated"
        else:
            summary = text[:200] + "..." if len(text) > 200 else text
            summary_type = "shortened"

    return jsonify({
        "summary": summary,
        "original_length": len(text),
        "summary_length": len(summary),
        "summary_type": summary_type,
        "speaker": speaker,
        "tool": "summarize"
    })

# Legacy compatibility
def main():
    import sys
    text = sys.argv[1] if len(sys.argv) > 1 else ""
    result = run({"input": text}, "cli_user")
    print(result.get_json()["summary"])

if __name__ == "__main__":
    main()
