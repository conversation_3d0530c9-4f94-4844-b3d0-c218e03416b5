name: Flag Audit

on:
  push:
    paths:
      - '**/*.py'
      - '**/*.js'
      - '**/*.html'
  pull_request:
    paths:
      - '**/*.py'
      - '**/*.js'
      - '**/*.html'

jobs:
  flag-audit:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.x'
      - name: Run flag audit script
        run: |
          python3 search_flag_audit.py
