from flask import jsonify
from datetime import datetime, timedelta
import re

def run(task, speaker):
    """
    Enhanced calendar event creator with date/time parsing
    """
    try:
        input_text = task.get("input", "")
        title = task.get("title", "")
        time_str = task.get("time", "")
        description = task.get("description", input_text)

        if not title and not input_text:
            return jsonify({"error": "No event title or description provided."})

        # Extract title from input if not provided
        if not title:
            # Simple title extraction - first sentence or first 50 chars
            sentences = input_text.split('.')
            title = sentences[0][:50] if sentences else input_text[:50]

        # Parse time from input text if not provided
        if not time_str:
            time_patterns = [
                r'(\d{1,2}:\d{2}\s*(?:AM|PM|am|pm))',
                r'(\d{1,2}\s*(?:AM|PM|am|pm))',
                r'(tomorrow|today|next week|next month)',
                r'(\d{1,2}/\d{1,2})',
                r'(monday|tuesday|wednesday|thursday|friday|saturday|sunday)'
            ]

            for pattern in time_patterns:
                match = re.search(pattern, input_text, re.IGNORECASE)
                if match:
                    time_str = match.group(1)
                    break

        # Default to 1 hour from now if no time found
        if not time_str:
            event_time = datetime.now() + timedelta(hours=1)
            time_str = event_time.strftime("%Y-%m-%d %H:%M")

        # Create event data
        event_data = {
            "title": title,
            "description": description,
            "time": time_str,
            "speaker": speaker,
            "created_at": datetime.now().isoformat(),
            "tool": "calendar_event"
        }

        # Here you would integrate with actual calendar service
        # (Google Calendar API, Outlook, etc.)

        return jsonify({
            "status": "Event created successfully",
            "event": event_data,
            "integration": "simulated",  # Change to actual service
            "speaker": speaker,
            "tool": "calendar_event"
        })

    except Exception as e:
        return jsonify({
            "error": f"Failed to create calendar event: {str(e)}",
            "speaker": speaker,
            "tool": "calendar_event"
        })

# Legacy compatibility
def main():
    import sys
    text = sys.argv[1] if len(sys.argv) > 1 else ""
    result = run({"input": text}, "cli_user")
    print(result.get_json().get("status", "Calendar event processing failed"))

if __name__ == "__main__":
    main()
